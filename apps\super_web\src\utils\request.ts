/** axios封装
 * 请求拦截、相应拦截、错误统一处理
 */
import axios, { type AxiosResponse, type AxiosRequestConfig } from "axios";
import QS from "qs";
import { message as Message } from 'ant-design-vue';
import Config from "@/settings/index";
import { errors_msg } from "./error_code_msg";

// code映射表
import { ERROR_CODE_MAP } from './errorCodes';

// TypeScript interfaces
interface ClientInfo {
    language: string;
    useragent: string;
    boundleid: string;
}

interface AuthInfo {
    auth_id?: string;
    auth_token?: string;
    device_type?: string;
}

interface ApiResponse<T = any> {
    error_code?: number;
    data?: T;
    req_method?: string;
    error_msg?: any;
    [key: string]: any;
}

interface ErrorConfig {
    msgKey: string;
    type: 'error' | 'warning' | 'success' | 'info';
    duration: number;
    customCheck?: (response: AxiosResponse<ApiResponse>) => boolean;
    customHandler?: (response: AxiosResponse<ApiResponse>) => string;
    handler?: (response: AxiosResponse<ApiResponse>) => void;
}

// 环境的切换
const client_info: ClientInfo = {
    language: "zh-CN",
    useragent: "Mozilla/5.0(Macintosh;IntelMacOSX10_11_5)AppleWebKit/537.36(KHTML,likeGecko)Chrome/65.0.3325.181Safari/537.36",
    boundleid: "com.liankexinxi.PicBox",
};
// const axios = axios.create({
//   baseURL: process.env.NODE_ENV === 'production' ? process.env.VUE_APP_BASE_API : '/', // api 的 base_url
//   timeout: Config.timeout // 请求超时时间
// })

axios.defaults.baseURL =
    process.env.NODE_ENV === "production" ? process.env.VUE_APP_BASE_API : "/"; // api 的 base_url
// axios.defaults.baseURL = 'https://grayka.shutuo.net/' // grayka
// axios.defaults.baseURL = 'https://ssdgray.hwwt2.com/' // hwwt 线上灰度环境
// axios.defaults.baseURL = 'http://8.153.19.142:8080/' // hwwt 线上灰度环境
// axios.defaults.baseURL = 'https://stargray.showtop.com/' // starbucks 线上灰度环境
// axios.defaults.baseURL = 'https://sbuxgray.showtop.com/' // starbucks 线上灰度环境
// axios.defaults.baseURL = 'https://sbux.showtop.com/' // starbucks 线上灰度环境
// axios.defaults.baseURL = 'http://192.168.10.206:8080' // starbucks 线上灰度环境
// axios.defaults.baseURL = "http://192.168.10.207:8080"; // starbucks 线上灰度环境
// axios.defaults.baseURL = 'http://192.168.10.202:32700' // starbucks 线上灰度环境
// axios.defaults.baseURL = 'https://sbux.showtop.com/'
// axios.defaults.baseURL = 'https://sbuxgray.showtop.com/'
// axios.defaults.baseURL = 'https://sbuxuat.showtop.com/'

axios.defaults.baseURL = '/';

// 请求超时时间
axios.defaults.timeout = Config.timeout; // 30s请求时间

// post请求头
axios.defaults.headers.get["Content-Type"] =
    "application/x-www-form-urlencoded; charset=UTF-8";
// 请求拦截器s
// axios.interceptors.request.use(
//   config => {
//     // 每次发送请求之前判断是否存在token，如果存在，则统一在http请求的header都加上token，不用每次请求都手动添加了
//     // 即使本地存在token，也有可能token是过期的，所以在响应拦截器中要对返回状态进行判断
//     // const token = store.state.token;
//     // token && (config.headers.Authorization = token);
//     // return config;
//   },
//   error => {
//     // return Promise.error(error)
//   }
// )

// 响应拦截器
axios.interceptors.response.use((response: AxiosResponse<ApiResponse>) => {
    if (response.status === 200) {
        // const DEFAULT_ERROR = {
        //     msgKey: 'unknownError',
        //     type: 'error',
        //     duration: 1000
        //   }

        const errorCode: number | undefined = response.data?.error_code;
        const errorConfig: ErrorConfig = errorCode !== undefined ? (ERROR_CODE_MAP as any)[errorCode] : undefined;

        if (errorConfig) {
            Message.destroy();

            // 处理自定义校验逻辑
            if (errorConfig.customCheck && errorConfig.customCheck(response)) {
                Message.warning({
                    content: errorConfig.customHandler!(response),
                    duration: 3000
                });
                return Promise.resolve(response);
            }

            // 执行通用处理
            const messageType = errorConfig.type || 'error';
            const messageContent = (errors_msg as any)[errorConfig.msgKey];
            const messageDuration = errorConfig.duration;

            switch (messageType) {
                case 'error':
                    Message.error({ content: messageContent, duration: messageDuration });
                    break;
                case 'warning':
                    Message.warning({ content: messageContent, duration: messageDuration });
                    break;
                case 'success':
                    Message.success({ content: messageContent, duration: messageDuration });
                    break;
                case 'info':
                    Message.info({ content: messageContent, duration: messageDuration });
                    break;
                default:
                    Message.error({ content: messageContent, duration: messageDuration });
            }

            // 执行特殊处理程序
            if (errorConfig.handler) {
                errorConfig.handler(response);
            }
        }

        return Promise.resolve(response);
    }
    return Promise.reject(response);
});

// axios.interceptors.response.use((response) => {
//     // console.log(response, 'response');
//     if (response.status === 200) {
//       switch (response.data.error_code) {
//         case 415:
//           Message.closeAll();
//           // if (store.state.longinStatus == true) {
//           Message({
//             message: transition_lang('loginExpired') ,
//             type: "warning",
//             duration: 1000,
//             forbidClick: true,
//           });
//           // 清除token
//           localStorage.removeItem("device_info");
//           sessionStorage.clear();
//           // store.state.longinStatus = false
//           setTimeout(() => {
//             store.dispatch("LogOut").then(() => {
//               location.reload(); // 为了重新实例化vue-router对象 避免bug
//             });
//             // router
//             //   .replace({
//             //     path: '/login'
//             //   })
//             //   .catch(err => { })
//           }, 500);
//           // }
//           break;
//         case 400:
//           Message.closeAll();
//           if (
//             response.data.req_method == "batch_upgrade" &&
//             response.data.error_msg.results == 1002
//           ) {
//             const msg = response.data.error_msg;
//             Message({
//               message: `${transition_lang('plan')}${msg.fst_param}:${msg.sec_param}${transition_lang('alreadyExists')}`,
//               type: "warning",
//               duration: 3000,
//               forbidClick: true,
//             });
//           } else {
//             Message({
//               message: transition_lang('apiRequestException'),
//               type: "error",
//               duration: 1000,
//               forbidClick: true,
//             });
//           }
//           break;
//         case 502:
//           Message.closeAll();
//           Message({
//             message:  transition_lang('apiRequestError'),
//             type: "error",
//             duration: 1000,
//             forbidClick: true,
//           });
//           break;
//         case 60000501:
//           Message.closeAll();
//           Message({
//             message: transition_lang('accessPermissionError'),
//             type: "error",
//             duration: 1000,
//             forbidClick: true,
//           });
//           break;
//       }

//       return Promise.resolve(response);
//     } else {
//       // Message({
//       //   message: '接口请求异常，请稍后重试',
//       //   type: 'error',
//       //   duration: 1000,
//       //   forbidClick: true
//       // })
//       return Promise.reject(response);
//     }
//   });

/**
 * get方法，对应get请求
 * @param url 请求的url地址
 * @param params 请求时携带的参数
 */
export function get<T = any>(url: string, params?: Record<string, any>): Promise<T> {
    // console.log(url)
    return new Promise((resolve, reject) => {
        axios
            .get<ApiResponse<T>>(url, {
                params: params
            })
            .then((res: AxiosResponse<ApiResponse<T>>) => {
                resolve(res.data.data as T);
            })
            .catch((err: any) => {
                reject(err.data);
            });
    });
}

/**
 * post方法，对应post请求
 * @param url 请求的url地址
 * @param params 请求时携带的参数
 * @param methods 请求方法名
 */
export function post<T = any>(url: string, params: any, methods: string): Promise<T> {
    const auth: string | null = localStorage.getItem("device_info");
    const authData: AuthInfo = auth ? JSON.parse(auth) : {};

    const data = {
        data: JSON.stringify({
            id: 4061,
            method: methods,
            params: [params],
            auth: authData,
            client_info: {
                language: "zh-CN",
                useragent: "Mozilla/5.0(Macintosh;IntelMacOSX10_11_5)AppleWebKit/537.36(KHTML,   likeGecko)Chrome/65.0.3325.181Safari/537.36",
                boundleid: "com.shutuo.liteplayer_mgmt",
            },
        }),
    };

    return new Promise((resolve, reject) => {
        axios
            .post<ApiResponse<T>>(url, QS.stringify(data))
            .then((res: AxiosResponse<ApiResponse<T>>) => {
                resolve(res.data.data as T);
            })
            .catch((err: any) => {
                reject(err.data);
            });
    });
}

/*
文件上传
*/
export function uploadFile<T = any>(url: string, src: File | Blob): Promise<T> {
    const authString: string | null = localStorage.getItem("device_info");
    if (!authString) {
        return Promise.reject(new Error("No authentication info found"));
    }

    const auth: AuthInfo = JSON.parse(authString);
    const time = new Date();
    const id = time.getTime();
    const formdata = new FormData();

    const data: Record<string, any> = {
        userfile: src,
        auth_id: auth.auth_id,
        auth_token: auth.auth_token,
        device_type: auth.device_type,
        client_info: JSON.stringify(client_info),
        source_type: 11,
        identify_str: "ds-admin-edit-content" + id,
        caption: "ds-admin-edit-content",
        tag_list: JSON.stringify([""]),
        min_size: 100,
    };

    for (const i in data) {
        formdata.append(i, data[i]);
    }

    // console.log(data)
    return new Promise((resolve, reject) => {
        axios({
            method: "post",
            url: url,
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
            data: formdata,
        })
            .then((res: AxiosResponse<T>) => {
                resolve(res.data);
            })
            .catch((err: any) => {
                reject(err.data);
            });
    });
}

/**
 * 下载文件的通用方法 接口要是鉴权
 */
export function downloadFilePromise(
    url: string,
    param?: Partial<AxiosRequestConfig>
): Promise<boolean> {
    return new Promise(function (resolve, reject) {
        const defaultParam: AxiosRequestConfig = {
            url,
            method: 'get',
            responseType: 'arraybuffer',
            timeout: 300000
        };

        axios(Object.assign(defaultParam, param))
            .then((res: AxiosResponse<ArrayBuffer>) => {
                const { headers } = res;
                const contentType: string = headers['content-type'];
                const blob = new Blob([res.data], {
                    type: contentType
                });

                const url01 = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url01;

                const contentDisposition = headers['content-disposition'];
                if (contentDisposition) {
                    const fileName = contentDisposition.split(';')[1]?.split('=')[1];
                    if (fileName) {
                        a.download = decodeURIComponent(fileName);
                    }
                }

                a.click();
                window.URL.revokeObjectURL(url01);
                resolve(true);
            })
            .catch((err: any) => {
                // const enc = new TextDecoder('utf-8');
                // const errMsg = JSON.parse(enc.decode(new Uint8Array(err.response.data)))
                reject(new Error('Download failed: ' + (err.message || 'Unknown error')));
            });
    });
}
