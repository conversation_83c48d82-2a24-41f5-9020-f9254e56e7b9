import type { AxiosResponse } from 'axios';
import { errors_msg } from "./error_code_msg";
import { useAuthStore } from '#/store';
import { $t } from '#/locales';

// 定义响应数据接口
interface ApiResponse<T = any> {
  error_code?: number;
  data?: T;
  req_method?: string;
  error_msg?: any;
  [key: string]: any;
}

// 定义错误配置接口
interface ErrorConfig {
  msgKey: string;
  type: 'error' | 'warning' | 'success' | 'info';
  duration: number;
  customCheck?: (response: AxiosResponse<ApiResponse>) => boolean;
  customHandler?: (response: AxiosResponse<ApiResponse>) => string;
  handler?: (response: AxiosResponse<ApiResponse>) => void;
}

console.log(errors_msg,'errors_msg')

export const ERROR_CODE_MAP: Record<number, ErrorConfig> = {
  415: {
    msgKey: 'loginExpired',
    type: 'warning',
    duration: 1000,
    handler: (_response: AxiosResponse<ApiResponse>) => {
      localStorage.removeItem("device_info");
      sessionStorage.clear();
      setTimeout(() => {
        const authStore = useAuthStore();
        authStore.logout().then(() => location.reload());
      }, 500)
    }
  },
  400: {
    msgKey: 'apiRequestException',
    type: 'error',
    duration: 1000,
    customCheck: (response: AxiosResponse<ApiResponse>) => {
      return response.data.req_method === "batch_upgrade" &&
        response.data.error_msg.results === 1002
    },
    customHandler: (response: AxiosResponse<ApiResponse>) => {
      const msg = response.data.error_msg;
      return `${$t('plan')}${msg.fst_param}:${msg.sec_param}${$t('alreadyExists')}`;
    }
  },
  502: {
    msgKey: 'apiRequestError',
    type: 'error',
    duration: 1000
  },


  //---------------------- Billing error_code definition

  // 促销活动相关错误 (1000-1005)
  1000: {
    msgKey: 'promotionIdRequired',
    type: 'error',
    duration: 1000,
  },
  1001: {
    msgKey: 'invalidPromotionId',
    type: 'error',
    duration: 1000
  },
  1002: {
    msgKey: 'noPurchaseInfo',
    type: 'warning',
    duration: 1000
  },
  1003: {
    msgKey: 'dbInsertError',
    type: 'error',
    duration: 1000
  },
  1004: {
    msgKey: 'noActivePromotions',
    type: 'warning',
    duration: 1000
  },
  1005: {
    msgKey: 'invalidPromotionTime',
    type: 'warning',
    duration: 1000
  },




  //---------------------- Common Excepts API Error Code
  // 基础错误码 (100-199)
  100: {
    msgKey: 'requestParamError',
    type: 'error',
    duration: 1000
  },
  101: {
    msgKey: 'nameExistsError',
    type: 'warning',
    duration: 1000
  },

  // 服务状态 (300-399)
  330: {
    msgKey: 'serverMaintenance',
    type: 'error',
    duration: 1000
  },
  401: {
    msgKey: 'deviceIdInvalid',
    type: 'error',
    duration: 1000
  },
  402: {
    msgKey: 'deviceTypeUnsupported',
    type: 'error',
    duration: 1000
  },
  403: {
    msgKey: 'authInfoMissing',
    type: 'error',
    duration: 1000
  },
  404: {
    msgKey: 'fileNotFound',
    type: 'error',
    duration: 1000
  },
  405: {
    msgKey: 'newUserSync',
    type: 'info',
    duration: 1000
  },
  410: {
    msgKey: 'authKeyError',
    type: 'error',
    duration: 1000
  },
  411: {
    msgKey: 'accessDenied',
    type: 'error',
    duration: 1000
  },
  412: {
    msgKey: 'clientBoundConflict',
    type: 'warning',
    duration: 1000
  },
  413: {
    msgKey: 'restoreAuthError',
    type: 'error',
    duration: 1000
  },
  414: {
    msgKey: 'clientRegistered',
    type: 'warning',
    duration: 1000
  },
  416: {
    msgKey: 'tokenTimeout',
    type: 'error',
    duration: 1000
  },

  // 业务逻辑错误 (430-499)
  430: {
    msgKey: 'userInfoMismatch',
    type: 'error',
    duration: 1000
  },
  441: {
    msgKey: 'serviceUnavailable',
    type: 'error',
    duration: 1000
  },
  442: {
    msgKey: 'emptyRequestBody',
    type: 'error',
    duration: 1000
  },
  471: {
    msgKey: 'cacheDataError',
    type: 'error',
    duration: 1000
  },
  475: {
    msgKey: 'requestBlocked',
    type: 'warning',
    duration: 1000
  },
  // 参数类错误 (600+)
  600: {
    msgKey: 'paramError',
    type: 'error',
    duration: 1000
  },

  //---------------------- Client API Error Code

  // 设备绑定相关错误 (417-419)
  417: {
    msgKey: 'clientRebindRequired',
    type: 'warning',
    duration: 1000
  },
  418: {
    msgKey: 'accountRebindRequired',
    type: 'warning',
    duration: 1000
  },

  // 认证校验错误 (419-429)
  419: {
    msgKey: 'invalidCredentials',
    type: 'error',
    duration: 1000
  },
  420: {
    msgKey: 'invalidEmailFormat',
    type: 'error',
    duration: 1000
  },
  421: {
    msgKey: 'invalidPasswordFormat',
    type: 'error',
    duration: 1000
  },
  422: {
    msgKey: 'emailAlreadyRegistered',
    type: 'warning',
    duration: 1000
  },

  // 账户恢复相关 (423-428)
  423: {
    msgKey: 'accountNotFound',
    type: 'error',
    duration: 1000
  },
  424: {
    msgKey: 'findbackRequestLimit',
    type: 'warning',
    duration: 1000
  },
  425: {
    msgKey: 'verifyRequestLimit',
    type: 'warning',
    duration: 1000
  },
  426: {
    msgKey: 'emailAlreadyVerified',
    type: 'info',
    duration: 1000
  },
  427: {
    msgKey: 'wrongOldPassword',
    type: 'error',
    duration: 1000
  },
  428: {
    msgKey: 'unauthorizedPasswordChange',
    type: 'error',
    duration: 1000
  },

  // 手机号相关 (429-434)
  429: {
    msgKey: 'phoneRegistered',
    type: 'warning',
    duration: 1000
  },
  433: {
    msgKey: 'phoneNotRegistered',
    type: 'error',
    duration: 1000
  },
  434: {
    msgKey: 'verifyRequestTooFrequent',
    type: 'warning',
    duration: 1000
  },

  // 用户信息校验 (435-437) - 修改编码避免重复
  435: {
    msgKey: 'userInfoMismatch',
    type: 'error',
    duration: 1000
  },
  436: {
    msgKey: 'invalidUsernameFormat',
    type: 'error',
    duration: 1000
  },
  437: {
    msgKey: 'usernameTaken',
    type: 'warning',
    duration: 1000
  },

  // 服务端错误 (440-444)
  440: {
    msgKey: 'serverWriteError',
    type: 'error',
    duration: 1000
  },
  443: {
    msgKey: 'invalidHashSignature',
    type: 'error',
    duration: 1000
  },
  444: {
    msgKey: 'expiredHashSignature',
    type: 'error',
    duration: 1000
  },


  // 其他业务错误 (470-522)
  470: {
    msgKey: 'invalidTaskName',
    type: 'error',
    duration: 1000
  },
  521: {
    msgKey: 'emailExists',
    type: 'warning',
    duration: 1000
  },
  522: {
    msgKey: 'duplicateLabel',
    type: 'warning',
    duration: 1000
  },


  // ---------------------- Gallery API Error Code

  // SNS 相关错误 (500-504)
  501: {
    msgKey: 'snsAuthFailed',
    type: 'error',
    duration: 1000
  },
  503: {
    msgKey: 'snsSaveError',
    type: 'error',
    duration: 1000
  },
  504: {
    msgKey: 'snsAuthLogicError',
    type: 'error',
    duration: 1000
  },

  // 上传限制 (510-511)
  510: {
    msgKey: 'uploadLimitExceeded',
    type: 'warning',
    duration: 1000
  },
  511: {
    msgKey: 'uploadProcessError',
    type: 'error',
    duration: 1000
  },

  // 共享功能错误 (526-530) - 修改编码避免重复
  526: {
    msgKey: 'shareNotReady',
    type: 'warning',
    duration: 1000
  },
  527: {
    msgKey: 'invalidShareId',
    type: 'error',
    duration: 1000
  },
  528: {
    msgKey: 'unsupportedP2P',
    type: 'warning',
    duration: 1000
  },
  529: {
    msgKey: 'shareIdNotExist',
    type: 'error',
    duration: 1000
  },

  // 资源管理 (530-550)
  530: {
    msgKey: 'imageNotFound',
    type: 'error',
    duration: 1000
  },
  540: {
    msgKey: 'guestUserRestriction',
    type: 'warning',
    duration: 1000
  },
  550: {
    msgKey: 'freeAccountLimit',
    type: 'warning',
    duration: 1000
  },

  // 客户端兼容性 (601-603)
  601: {
    msgKey: 'unsupportedClientVersion',
    type: 'error',
    duration: 1000
  },
  602: {
    msgKey: 'unsupportedResolution',
    type: 'warning',
    duration: 1000
  },
  603: {
    msgKey: 'latestVersion',
    type: 'info',
    duration: 1000
  },

  // 其他业务错误 (560-582)
  560: {
    msgKey: 'snsFriendImportError',
    type: 'error',
    duration: 1000
  },
  580: {
    msgKey: 'noP2PContent',
    type: 'warning',
    duration: 1000
  },
  581: {
    msgKey: 'invalidP2PStatus',
    type: 'error',
    duration: 1000
  },
  582: {
    msgKey: 'userIdMismatch',
    type: 'error',
    duration: 1000
  },

  //----------------------File sync service error_code definition
  3001: {
    msgKey: 'invalidParameters',
    type: 'error',
    duration: 1000,

  },
  3008: {
    msgKey: 'fileAlreadyExists',
    type: 'warning',
    duration: 1000
  },
  // ---------------------- personal file service error_code definition

  4001: {
    msgKey: 'methodMustBePost',
    type: 'error',
    duration: 1000,

  },
  4002: {
    msgKey: 'unsupportedMethod',
    type: 'error',
    duration: 1000,

  },
  4003: {
    msgKey: 'uploadFailed',
    type: 'error',
    duration: 1000
  },
  4004: {
    msgKey: 'methodNotDefined',
    type: 'error',
    duration: 1000
  },

  //---------------------- Pushout API Error Code
  701: {
    msgKey: 'invalidInputParams',
    type: 'error',
    duration: 1000
  },

  // 任务处理错误 (702-704)
  702: {
    msgKey: 'taskSaveFailed',
    type: 'error',
    duration: 1000
  },
  704: {
    msgKey: 'mqPublishError',
    type: 'error',
    duration: 1000
  },

  // 会话与认证错误 (703,706)
  703: {
    msgKey: 'tokenExpired',
    type: 'error',
    duration: 1000
  },
  706: {
    msgKey: 'deviceSessionError',
    type: 'error',
    duration: 1000
  },

  // 系统级异常 (705,707)
  705: {
    msgKey: 'phpRuntimeError',
    type: 'error',
    duration: 1000
  },
  707: {
    msgKey: 'pythonRuntimeError',
    type: 'error',
    duration: 1000
  },

  //---------------------- Comments error_code definition
  // 输入验证相关错误 (514-519)
  514: {
    msgKey: 'invalidInputParams',
    type: 'error',
    duration: 1000
  },
  515: {
    msgKey: 'invalidPOI',
    type: 'error',
    duration: 1000
  },
  516: {
    msgKey: 'commentTooLong',
    type: 'warning',
    duration: 1000,
  },
  517: {
    msgKey: 'invalidLikeValue',
    type: 'error',
    duration: 1000
  },
  518: {
    msgKey: 'duplicateComment',
    type: 'warning',
    duration: 1000
  },
  519: {
    msgKey: 'requestTooFrequent',
    type: 'warning',
    duration: 1000
  },

  // 内容交互错误 (531-533) - 修改编码避免重复
  531: {
    msgKey: 'missingContent',
    type: 'error',
    duration: 1000
  },
  532: {
    msgKey: 'dataException',
    type: 'error',
    duration: 1000
  },
  533: {
    msgKey: 'duplicateVote',
    type: 'warning',
    duration: 1000
  },
//     ----------------------  For Partner Error Code Define
  // 用户注册与权限错误 (5001-5006)
  5001: {
    msgKey: 'usernameRequired',
    type: 'error',
    duration: 1000
  },
  5002: {
    msgKey: 'usernameLengthError',
    type: 'error',
    duration: 1000,
  },
  5003: {
    msgKey: 'usernameExists',
    type: 'warning',
    duration: 1000
  },
  5004: {
    msgKey: 'emailRequired',
    type: 'error',
    duration: 1000
  },
  5005: {
    msgKey: 'emailExists',
    type: 'warning',
    duration: 1000
  },
  5006: {
    msgKey: 'freeUserDenied',
    type: 'error',
    duration: 1000
  },

  //---------------------- DMB ErrorCode
  60000404: {
    msgKey: 'invalidDataRequest',
    type: 'error',
    duration: 1000
  },
  60000406: {
    msgKey: 'invalidStoreCode',
    type: 'error',
    duration: 1000
  },
  // 数据处理相关
  60000500: {
    msgKey: 'dataProcessError',
    type: 'error',
    duration: 1000
  },
  60000501: {
    msgKey: 'accessPermissionError',
    type: 'error',
    duration: 1000
  },
  60000502: {
    msgKey: 'functionPermissionError',
    type: 'error',
    duration: 1000
  },

  // 认证相关
  60000504: {
    msgKey: 'invalidParams',
    type: 'warning',
    duration: 1000
  },
  60000505: {
    msgKey: 'invalidSSOCode',
    type: 'error',
    duration: 1000
  },
  60000506: {
    msgKey: 'invalidIdentity',
    type: 'error',
    duration: 1000
  },
  // 菜单管理
  60001001: {
    msgKey: 'menuTitleDuplicate',
    type: 'warning',
    duration: 1000
  },
  60001002: {
    msgKey: 'menuPermDuplicate',
    type: 'warning',
    duration: 1000
  },

  // 注册相关 (修正疑似错误编码)
  60004605: { // 原600004605调整为标准6位编码
    msgKey: 'mobileRegistered',
    type: 'warning',
    duration: 1000
  },
  ********: {
    msgKey: 'emailRegistered',
    type: 'warning',
    duration: 1000
  },
  *********: { // 保持原始编码
    msgKey: 'usernameRegistered',
    type: 'warning',
    duration: 1000
  },
  // 素材管理
  *********: {
    msgKey: 'materialReviewed',
    type: 'info',
    duration: 1000
  },
  *********: {
    msgKey: 'invalidPendingMaterial',
    type: 'warning',
    duration: 1000
  },

  // 账号状态
  ********: {
    msgKey: 'accountNotAllowed',
    type: 'error',
    duration: 1000
  },
  ********: {
    msgKey: 'accountInactive',
    type: 'warning',
    duration: 1000
  },
  ********: {
    msgKey: 'accountLocked',
    type: 'error',
    duration: 1000
  },
  ********: {
    msgKey: 'invalidCredentials',
    type: 'error',
    duration: 1000
  },

  // 接口异常
  ********: {
    msgKey: 'apiException',
    type: 'error',
    duration: 1000
  },
  ********: {
    msgKey: 'authError',
    type: 'error',
    duration: 1000
  },
  ********: {
    msgKey: 'paramError',
    type: 'error',
    duration: 1000
  },

  // 商品管理
  ********: {
    msgKey: 'productCodeExists',
    type: 'warning',
    duration: 1000
  },
  ********: {
    msgKey: 'productNameExists',
    type: 'warning',
    duration: 1000
  },
  ********: {
    msgKey: 'productIdNotExist',
    type: 'error',
    duration: 1000
  },
  ********: {
    msgKey: 'productSoldOut',
    type: 'info',
    duration: 1000
  }


  // 添加更多错误码...
};
